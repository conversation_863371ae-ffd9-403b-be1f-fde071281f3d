import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Amenity from '@/models/Amenity';
import { withAuth, withErrorHandling, withCors, AuthenticatedRequest } from '@/middleware/auth';
import { validateRequest, updateAmenitySchema } from '@/lib/validation';
import mongoose from 'mongoose';

// GET /api/amenities/[id] - Get single amenity
async function getAmenityHandler(req: AuthenticatedRequest, { params }: { params: { id: string } }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid amenity ID' },
        { status: 400 }
      );
    }

    const amenity = await Amenity.findById(id).lean();

    if (!amenity) {
      return NextResponse.json(
        { error: 'Amenity not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: amenity
    });

  } catch (error) {
    console.error('Get amenity error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch amenity' },
      { status: 500 }
    );
  }
}

// PUT /api/amenities/[id] - Update amenity (admin only)
async function updateAmenityHandler(req: AuthenticatedRequest, { params }: { params: { id: string } }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid amenity ID' },
        { status: 400 }
      );
    }

    const body = await req.json();
    
    // Validate request body
    const validation = validateRequest(body, updateAmenitySchema);
    if (validation.error) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    const amenity = await Amenity.findByIdAndUpdate(
      id,
      validation.value,
      { new: true, runValidators: true }
    );

    if (!amenity) {
      return NextResponse.json(
        { error: 'Amenity not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Amenity updated successfully',
      data: amenity
    });

  } catch (error) {
    console.error('Update amenity error:', error);
    
    if (error instanceof Error && error.message.includes('duplicate key')) {
      return NextResponse.json(
        { error: 'An amenity with this name already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update amenity' },
      { status: 500 }
    );
  }
}

// DELETE /api/amenities/[id] - Delete amenity (admin only)
async function deleteAmenityHandler(req: AuthenticatedRequest, { params }: { params: { id: string } }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid amenity ID' },
        { status: 400 }
      );
    }

    const amenity = await Amenity.findByIdAndDelete(id);

    if (!amenity) {
      return NextResponse.json(
        { error: 'Amenity not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Amenity deleted successfully'
    });

  } catch (error) {
    console.error('Delete amenity error:', error);
    return NextResponse.json(
      { error: 'Failed to delete amenity' },
      { status: 500 }
    );
  }
}

// Route handlers
export const GET = withMiddleware(
  withCors,
  withAuth,
  withErrorHandling
)(getAmenityHandler);

export const PUT = withMiddleware(
  withCors,
  withAuth,
  withErrorHandling
)(updateAmenityHandler);

export const DELETE = withMiddleware(
  withCors,
  withAuth,
  withErrorHandling
)(deleteAmenityHandler);

// Helper function to combine middlewares
function withMiddleware(...middlewares: Array<(handler: any) => any>) {
  return function(handler: (req: AuthenticatedRequest, context: any) => Promise<NextResponse>) {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}
