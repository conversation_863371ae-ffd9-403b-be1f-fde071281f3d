import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Amenity from '@/models/Amenity';
import { withAuth, withErrorHandling, withCors, AuthenticatedRequest, withOptionalAuth } from '@/middleware/auth';
import { validateRequest, createAmenitySchema } from '@/lib/validation';

// GET /api/amenities - Get all amenities
async function getAmenitiesHandler(req: AuthenticatedRequest) {
  await connectDB();

  try {
    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category');
    const active = searchParams.get('active');

    // Build query
    const query: any = {};
    
    if (category) {
      query.category = category.toLowerCase();
    }
    
    if (active !== null) {
      query.isActive = active === 'true';
    }

    const amenities = await Amenity.find(query)
      .sort({ category: 1, name: 1 })
      .lean();

    // Group by category if requested
    const groupByCategory = searchParams.get('groupByCategory') === 'true';
    
    if (groupByCategory) {
      const grouped = amenities.reduce((acc: any, amenity: any) => {
        const category = amenity.category;
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(amenity);
        return acc;
      }, {});

      return NextResponse.json({
        success: true,
        data: grouped
      });
    }

    return NextResponse.json({
      success: true,
      data: amenities
    });

  } catch (error) {
    console.error('Get amenities error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch amenities' },
      { status: 500 }
    );
  }
}

// POST /api/amenities - Create new amenity (admin only)
async function createAmenityHandler(req: AuthenticatedRequest) {
  await connectDB();

  try {
    const body = await req.json();
    
    // Validate request body
    const validation = validateRequest(body, createAmenitySchema);
    if (validation.error) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    const amenity = await Amenity.create(validation.value);

    return NextResponse.json({
      success: true,
      message: 'Amenity created successfully',
      data: amenity
    }, { status: 201 });

  } catch (error) {
    console.error('Create amenity error:', error);
    
    if (error instanceof Error && error.message.includes('duplicate key')) {
      return NextResponse.json(
        { error: 'An amenity with this name already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create amenity' },
      { status: 500 }
    );
  }
}

// Route handlers
export const GET = withMiddleware(
  withCors,
  withOptionalAuth,
  withErrorHandling
)(getAmenitiesHandler);

export const POST = withMiddleware(
  withCors,
  withAuth,
  withErrorHandling
)(createAmenityHandler);

// Helper function to combine middlewares
function withMiddleware(...middlewares: Array<(handler: any) => any>) {
  return function(handler: (req: AuthenticatedRequest) => Promise<NextResponse>) {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}
