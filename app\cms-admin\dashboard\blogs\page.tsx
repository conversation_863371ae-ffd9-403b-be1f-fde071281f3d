'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import AnimatedBackground from '@/components/ui/animated-tabs'
import { Input } from '@/components/ui/input'
import { 
  Loader2, 
  Search, 
  Plus, 
  FileText, 
  Edit, 
  Trash2, 
  Calendar, 
  User, 
  Eye, 
  Share2, 
  Tag,
  Filter,
  Clock,
  CalendarClock,
  ArrowUpRight,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'
import { Toaster, toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  authorId: string;
  category: string;
  status: 'draft' | 'published' | 'scheduled';
  publishDate: string;
  createdAt: string;
  updatedAt: string;
  imageUrl: string;
  readingTime: string;
  tags: string[];
  metaTitle?: string;
  metaDescription?: string;
}

// Service to interact with localStorage for blog data
const BlogService = {
  getAllBlogs: (): BlogPost[] => {
    if (typeof window === 'undefined') return [];
    const blogs = localStorage.getItem('blogPosts');
    return blogs ? JSON.parse(blogs) : [];
  },
  
  saveBlog: (blog: BlogPost): void => {
    if (typeof window === 'undefined') return;
    const blogs = BlogService.getAllBlogs();
    const existingBlogIndex = blogs.findIndex(b => b.id === blog.id);
    
    if (existingBlogIndex >= 0) {
      blogs[existingBlogIndex] = {
        ...blog,
        updatedAt: new Date().toISOString()
      };
    } else {
      blogs.push({
        ...blog,
        id: crypto.randomUUID(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    localStorage.setItem('blogPosts', JSON.stringify(blogs));
  },
  
  deleteBlog: (id: string): void => {
    if (typeof window === 'undefined') return;
    const blogs = BlogService.getAllBlogs();
    const filteredBlogs = blogs.filter(blog => blog.id !== id);
    localStorage.setItem('blogPosts', JSON.stringify(filteredBlogs));
  },
  
  getBlogById: (id: string): BlogPost | null => {
    const blogs = BlogService.getAllBlogs();
    return blogs.find(blog => blog.id === id) || null;
  },
  
  // Initialize with sample data if no blogs exist
  initSampleData: (): void => {
    if (typeof window === 'undefined') return;
    const blogs = BlogService.getAllBlogs();
    
    if (blogs.length === 0) {
      const sampleBlogs: BlogPost[] = [
        {
          id: crypto.randomUUID(),
          title: "The Future of Sustainable Architecture in Urban Development",
          slug: "the-future-of-sustainable-architecture-in-urban-development",
          excerpt: "Exploring innovative designs and materials that are shaping eco-friendly cityscapes...",
          content: "## The Future of Sustainable Architecture\n\nAs cities continue to expand...",
          author: "Alex Johnson",
          authorId: "user-1",
          category: "Architecture",
          status: "published",
          publishDate: new Date().toISOString(),
          createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          imageUrl: "https://images.unsplash.com/photo-1506748686214-e9df14d4d9d0",
          readingTime: "7 min read",
          tags: ["sustainable", "architecture", "urban", "eco-friendly"],
          metaTitle: "Sustainable Architecture in Urban Development | Laxmi Developers",
          metaDescription: "Explore the future of sustainable architecture in urban development with eco-friendly designs and materials for greener cities."
        },
        {
          id: crypto.randomUUID(),
          title: "Smart Homes: Integrating Technology for a Modern Lifestyle",
          slug: "smart-homes-integrating-technology-for-a-modern-lifestyle",
          excerpt: "A look into how IoT devices, AI, and automation are transforming residential spaces...",
          content: "## Smart Homes: The Future is Here\n\nThe integration of IoT devices...",
          author: "Priya Sharma",
          authorId: "user-2",
          category: "Technology",
          status: "published",
          publishDate: new Date().toISOString(),
          createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          imageUrl: "https://images.unsplash.com/photo-1529400971027-cadd71752d99",
          readingTime: "5 min read",
          tags: ["smart home", "technology", "IoT", "automation"],
          metaTitle: "Smart Home Technology Integration | Laxmi Developers",
          metaDescription: "Discover how IoT devices and automation are transforming modern homes into smart living spaces."
        },
        {
          id: crypto.randomUUID(),
          title: "Upcoming Trends in Luxury Real Estate for 2024",
          slug: "upcoming-trends-in-luxury-real-estate-for-2024",
          excerpt: "Predicting the luxury real estate market trends for the coming year...",
          content: "## Luxury Real Estate Trends for 2024\n\nThe luxury real estate market is constantly evolving...",
          author: "Raj Patel",
          authorId: "user-3",
          category: "Real Estate",
          status: "draft",
          publishDate: "",
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          imageUrl: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750",
          readingTime: "8 min read",
          tags: ["luxury", "real estate", "trends", "2024", "market"],
          metaTitle: "2024 Luxury Real Estate Trends | Laxmi Developers",
          metaDescription: "Explore our predictions for luxury real estate market trends in 2024 and beyond."
        },
        {
          id: crypto.randomUUID(),
          title: "Sustainable Landscaping for Modern Properties",
          slug: "sustainable-landscaping-for-modern-properties",
          excerpt: "How eco-friendly landscaping enhances property value and environmental impact...",
          content: "## Sustainable Landscaping\n\nModern landscaping goes beyond aesthetics...",
          author: "Meera Joshi",
          authorId: "user-4",
          category: "Architecture",
          status: "scheduled",
          publishDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          imageUrl: "https://images.unsplash.com/photo-1558036117-15d82a90b9b1",
          readingTime: "5 min read",
          tags: ["landscaping", "sustainable", "eco-friendly", "property value"],
          metaTitle: "Sustainable Landscaping for Modern Properties | Laxmi Developers",
          metaDescription: "Learn how sustainable landscaping can enhance property value while minimizing environmental impact."
        }
      ];
      
      localStorage.setItem('blogPosts', JSON.stringify(sampleBlogs));
    }
  }
};

export default function BlogsAdminPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  // Sample categories for the filter
  const categories = [
    'Architecture',
    'Interior Design',
    'Real Estate',
    'Technology',
    'Lifestyle'
  ];

  useEffect(() => {
    // Initialize sample data if needed
    BlogService.initSampleData();
    
    // Fetch blog posts from localStorage
    setIsLoading(true);
    try {
      const posts = BlogService.getAllBlogs();
      setBlogPosts(posts);
    } catch (error) {
      console.error('Error loading blog posts:', error);
      toast.error('Failed to load blog posts');
    } finally {
      setIsLoading(false);
    }
    
    // Setup a check for scheduled posts that need publishing
    const checkScheduledPosts = () => {
      const posts = BlogService.getAllBlogs();
      const now = new Date();
      
      let postsUpdated = false;
      
      posts.forEach(post => {
        if (post.status === 'scheduled' && new Date(post.publishDate) <= now) {
          post.status = 'published';
          postsUpdated = true;
          BlogService.saveBlog(post);
        }
      });
      
      if (postsUpdated) {
        setBlogPosts(BlogService.getAllBlogs());
        toast.success('Scheduled post(s) published automatically');
      }
    };
    
    // Check immediately and set up interval
    checkScheduledPosts();
    const interval = setInterval(checkScheduledPosts, 60000); // Check every minute
    
    return () => clearInterval(interval);
  }, []);

  // Filter blog posts based on active tab, search query, and category
  const filteredPosts = blogPosts.filter(post => {
    const matchesTab = 
      activeTab === 'all' || 
      (activeTab === 'published' && post.status === 'published') ||
      (activeTab === 'drafts' && post.status === 'draft') ||
      (activeTab === 'scheduled' && post.status === 'scheduled');
    
    const matchesSearch = 
      searchQuery === '' || 
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = 
      categoryFilter === 'all' || 
      post.category === categoryFilter;
    
    return matchesTab && matchesSearch && matchesCategory;
  });

  // Handle delete confirmation
  const handleDelete = (id: string) => {
    if (confirmDelete === id) {
      try {
        BlogService.deleteBlog(id);
        setBlogPosts(blogPosts.filter(post => post.id !== id));
        toast.success('Blog post deleted successfully');
      } catch (error) {
        console.error('Error deleting blog post:', error);
        toast.error('Failed to delete blog post');
      }
      setConfirmDelete(null);
    } else {
      setConfirmDelete(id);
      // Reset confirmation after 3 seconds
      setTimeout(() => setConfirmDelete(null), 3000);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get appropriate badge for post status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Published
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
            <FileText className="w-3 h-3 mr-1" />
            Draft
          </Badge>
        );
      case 'scheduled':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <CalendarClock className="w-3 h-3 mr-1" />
            Scheduled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
            {status}
          </Badge>
        );
    }
  };

  // Handle scheduled publishing
  const scheduleBlog = (post: BlogPost, publishDate: string) => {
    const updatedPost = {
      ...post,
      status: 'scheduled',
      publishDate,
      updatedAt: new Date().toISOString()
    };
    
    try {
      BlogService.saveBlog(updatedPost);
      setBlogPosts(
        blogPosts.map(p => (p.id === post.id ? updatedPost : p))
      );
      toast.success('Blog post scheduled for publishing');
    } catch (error) {
      console.error('Error scheduling blog post:', error);
      toast.error('Failed to schedule blog post');
    }
  };

  // Handle publish now
  const publishNow = (post: BlogPost) => {
    const updatedPost = {
      ...post,
      status: 'published',
      publishDate: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    try {
      BlogService.saveBlog(updatedPost);
      setBlogPosts(
        blogPosts.map(p => (p.id === post.id ? updatedPost : p))
      );
      toast.success('Blog post published successfully');
    } catch (error) {
      console.error('Error publishing blog post:', error);
      toast.error('Failed to publish blog post');
    }
  };

  // Edit blog post
  const handleEdit = (id: string) => {
    router.push(`/cms-admin/dashboard/blogs/edit/${id}`);
  };

  return (
    <>
      <Toaster position="top-right" expand={true} richColors />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Blog Posts</h1>
            <p className="text-gray-600 mt-1">
              Create, edit, and manage your blog content
            </p>
          </div>
          <Link href="/cms-admin/dashboard/blogs/create">
            <Button className="space-x-2 w-full sm:w-auto">
              <Plus className="w-4 h-4" />
              <span>Create New Post</span>
            </Button>
          </Link>
        </div>
        
        {/* Tabs */}
        <div className="flex flex-wrap border-b border-gray-200">
          <button
            className={`px-4 py-2 font-medium text-sm rounded-t-lg ${
              activeTab === 'all'
                ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('all')}
          >
            All Posts
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm rounded-t-lg ${
              activeTab === 'published'
                ? 'bg-green-50 text-green-700 border-b-2 border-green-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('published')}
          >
            Published
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm rounded-t-lg ${
              activeTab === 'drafts'
                ? 'bg-amber-50 text-amber-700 border-b-2 border-amber-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('drafts')}
          >
            Drafts
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm rounded-t-lg ${
              activeTab === 'scheduled'
                ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('scheduled')}
          >
            Scheduled
          </button>
        </div>
        
        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search posts by title, excerpt, author, or tags..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-9 h-10"
            />
          </div>
          <select
            value={categoryFilter}
            onChange={e => setCategoryFilter(e.target.value)}
            className="h-10 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-3 pr-8 text-sm w-full sm:w-auto"
          >
            <option value="all">All Categories</option>
            {categories.map((cat, index) => (
              <option key={index} value={cat}>
                {cat}
              </option>
            ))}
          </select>
        </div>
        
        {/* Blog Post List */}
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-3 text-gray-600">Loading blog posts...</span>
          </div>
        ) : filteredPosts.length === 0 ? (
          <Card className="flex flex-col items-center justify-center p-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No blog posts found</h3>
            <p className="text-gray-600 mb-6 max-w-md">
              {searchQuery || categoryFilter !== 'all'
                ? "No posts match your current filters. Try adjusting your search criteria."
                : "You haven't created any blog posts yet. Click the button below to create your first post."}
            </p>
            <Link href="/cms-admin/dashboard/blogs/create">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create New Post
              </Button>
            </Link>
          </Card>
        ) : (
          <div className="grid gap-6">
            {filteredPosts.map(post => (
              <Card key={post.id} className="overflow-hidden">
                <div className="grid grid-cols-12 h-full">
                  {/* Image Column */}
                  <div className="col-span-12 sm:col-span-3 lg:col-span-2 relative">
                    <div className="w-full h-40 sm:h-full bg-gray-200 relative">
                      {post.imageUrl ? (
                        <img
                          src={post.imageUrl}
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400">
                          <FileText className="h-12 w-12" />
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Content Column */}
                  <div className="col-span-12 sm:col-span-9 lg:col-span-10 p-4 sm:p-6 flex flex-col">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2 gap-2">
                      <div className="flex flex-wrap gap-2">
                        {getStatusBadge(post.status)}
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {post.category}
                        </Badge>
                        {post.readingTime && (
                          <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
                            <Clock className="w-3 h-3 mr-1" />
                            {post.readingTime}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-8 text-gray-700 border-gray-300 hover:bg-gray-50"
                          onClick={() => handleEdit(post.id)}
                        >
                          <Edit className="w-3.5 h-3.5 mr-1" />
                          Edit
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          className={`text-xs h-8 ${
                            confirmDelete === post.id
                              ? 'bg-red-50 text-red-700 border-red-300 hover:bg-red-100'
                              : 'text-red-600 border-red-300 hover:bg-red-50'
                          }`}
                          onClick={() => handleDelete(post.id)}
                        >
                          <Trash2 className="w-3.5 h-3.5 mr-1" />
                          {confirmDelete === post.id ? 'Confirm' : 'Delete'}
                        </Button>
                        
                        {post.status === 'draft' && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs h-8 text-green-700 border-green-300 hover:bg-green-50"
                            onClick={() => publishNow(post)}
                          >
                            <ArrowUpRight className="w-3.5 h-3.5 mr-1" />
                            Publish
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <h2 className="text-lg font-semibold text-gray-900 line-clamp-1">
                      {post.title}
                    </h2>
                    
                    <p className="text-gray-600 line-clamp-2 mt-1 flex-grow">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex flex-col sm:flex-row justify-between mt-4 pt-4 border-t border-gray-100 text-xs text-gray-500 gap-2">
                      <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
                        <div className="flex items-center">
                          <User className="w-3.5 h-3.5 mr-1.5 text-gray-400" />
                          <span>{post.author}</span>
                        </div>
                        
                        <div className="flex items-center">
                          <Calendar className="w-3.5 h-3.5 mr-1.5 text-gray-400" />
                          {post.status === 'scheduled' ? (
                            <span>Scheduled: {formatDate(post.publishDate)}</span>
                          ) : post.status === 'published' ? (
                            <span>Published: {formatDate(post.publishDate)}</span>
                          ) : (
                            <span>Created: {formatDate(post.createdAt)}</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1.5">
                        {post.tags.slice(0, 3).map((tag, idx) => (
                          <span
                            key={idx}
                            className="inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-800 text-xs"
                          >
                            <Tag className="w-2.5 h-2.5 mr-1" />
                            {tag}
                          </span>
                        ))}
                        {post.tags.length > 3 && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-800 text-xs">
                            +{post.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </>
  );
}
