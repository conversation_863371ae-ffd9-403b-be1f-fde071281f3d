'use client'

import React, { useState, useEffect } from 'react'
import { Toaster, toast } from 'sonner'
import { Plus, Search, Filter, Edit, Eye, Trash2, DownloadCloud } from 'lucide-react'
import { AddEditProjectForm } from '@/app/cms-admin/components/AddEditProjectForm'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

// Project service to interact with localStorage
const ProjectService = {
  getAllProjects: () => {
    if (typeof window === 'undefined') return [];
    const projects = localStorage.getItem('projectsData');
    return projects ? JSON.parse(projects) : [];
  },
  
  saveProject: (project) => {
    if (typeof window === 'undefined') return;
    const projects = ProjectService.getAllProjects();
    const existingProjectIndex = projects.findIndex(p => p.id === project.id);
    
    if (existingProjectIndex >= 0) {
      projects[existingProjectIndex] = {
        ...project,
        updatedAt: new Date().toISOString()
      };
    } else {
      projects.push({
        ...project,
        id: crypto.randomUUID(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }
    
    localStorage.setItem('projectsData', JSON.stringify(projects));
    return project.id;
  },
  
  deleteProject: (id) => {
    if (typeof window === 'undefined') return;
    const projects = ProjectService.getAllProjects();
    const filteredProjects = projects.filter(project => project.id !== id);
    localStorage.setItem('projectsData', JSON.stringify(filteredProjects));
  },
  
  getProjectById: (id) => {
    const projects = ProjectService.getAllProjects();
    return projects.find(project => project.id === id) || null;
  },
  
  // Initialize with sample data if no projects exist
  initSampleData: () => {
    if (typeof window === 'undefined') return;
    const projects = ProjectService.getAllProjects();
    
    if (projects.length === 0) {
      const sampleProjects = [
        {
          id: crypto.randomUUID(),
          title: 'Skyline Towers',
          category: 'residential',
          status: 'completed',
          description: 'Luxury residential towers with panoramic city views',
          coverImage: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',
          location: {
            address: 'Vesu, Surat, Gujarat',
            lat: '21.1702',
            lng: '72.8311',
          },
          reraNumber: 'RERA123456',
          specifications: {
            totalUnits: '120',
            unitTypes: '2, 3 & 4 BHK',
            unitArea: '1200-2500 sq ft',
            possession: 'Ready to Move',
            structure: 'RCC Framed Structure',
            flooring: 'Vitrified Tiles',
          },
          contactSales: '+91 9876543210',
          amenities: [1, 2, 4, 5, 7],
          brochureUrl: 'https://example.com/brochure.pdf',
          createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: crypto.randomUUID(),
          title: 'Green Valley Apartments',
          category: 'residential',
          status: 'ongoing',
          description: 'Eco-friendly apartments with spacious layouts',
          coverImage: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&h=600&fit=crop',
          location: {
            address: 'Adajan, Surat, Gujarat',
            lat: '21.2056',
            lng: '72.7923',
          },
          reraNumber: 'RERA789012',
          specifications: {
            totalUnits: '80',
            unitTypes: '2 & 3 BHK',
            unitArea: '1100-1800 sq ft',
            possession: 'Dec 2024',
            structure: 'RCC Framed Structure',
            flooring: 'Vitrified Tiles',
          },
          contactSales: '+91 9876543211',
          amenities: [1, 3, 6, 7, 8],
          brochureUrl: 'https://example.com/brochure.pdf',
          createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: crypto.randomUUID(),
          title: 'Corporate Plaza',
          category: 'commercial',
          status: 'upcoming',
          description: 'Premium office spaces in the heart of the city',
          coverImage: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop',
          location: {
            address: 'Ring Road, Surat, Gujarat',
            lat: '21.1784',
            lng: '72.8397',
          },
          reraNumber: 'RERA345678',
          specifications: {
            totalUnits: '50',
            unitTypes: 'Office Spaces',
            unitArea: '500-2000 sq ft',
            possession: 'Jun 2025',
            structure: 'RCC Framed Structure',
            flooring: 'Vitrified Tiles',
          },
          contactSales: '+91 9876543212',
          amenities: [5, 7, 8],
          brochureUrl: 'https://example.com/brochure.pdf',
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        },
      ];
      
      localStorage.setItem('projectsData', JSON.stringify(sampleProjects));
    }
  }
};

export default function ProjectsList() {
  const router = useRouter();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editProjectId, setEditProjectId] = useState(null);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [confirmDelete, setConfirmDelete] = useState(null);

  useEffect(() => {
    // Initialize sample data if needed
    ProjectService.initSampleData();
    
    // Load projects
    const loadProjects = () => {
      try {
        const projectsData = ProjectService.getAllProjects();
        setProjects(projectsData);
      } catch (error) {
        console.error('Error loading projects:', error);
        toast.error('Failed to load projects');
      } finally {
        setLoading(false);
      }
    };
    
    loadProjects();
  }, []);

  // Handle filtering projects
  const filteredProjects = projects.filter(project => {
    const matchesSearch = searchTerm === '' || 
      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.reraNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || 
      project.category.toLowerCase() === categoryFilter.toLowerCase();
    
    const matchesStatus = statusFilter === 'all' || 
      project.status.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Handle delete confirmation
  const handleDelete = (id) => {
    if (confirmDelete === id) {
      try {
        ProjectService.deleteProject(id);
        setProjects(projects.filter(project => project.id !== id));
        toast.success('Project deleted successfully');
      } catch (error) {
        console.error('Error deleting project:', error);
        toast.error('Failed to delete project');
      }
      setConfirmDelete(null);
    } else {
      setConfirmDelete(id);
      setTimeout(() => setConfirmDelete(null), 3000);
    }
  };

  // Handle form close/submit
  const handleFormClose = (refreshData = false) => {
    setShowAddForm(false);
    setEditProjectId(null);
    
    if (refreshData) {
      setLoading(true);
      const projectsData = ProjectService.getAllProjects();
      setProjects(projectsData);
      setLoading(false);
    }
  };

  // Handle edit project
  const handleEdit = (id) => {
    setEditProjectId(id);
    setShowAddForm(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'ongoing':
        return 'bg-blue-100 text-blue-800';
      case 'upcoming':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category.toLowerCase()) {
      case 'residential':
        return 'bg-purple-100 text-purple-800';
      case 'commercial':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchTerm('');
    setCategoryFilter('all');
    setStatusFilter('all');
  };

  return (
    <>
      <Toaster position="top-right" expand={true} richColors />
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
            <p className="text-gray-600">Manage your real estate projects</p>
          </div>
          <button 
            onClick={() => {
              setEditProjectId(null);
              setShowAddForm(true);
            }} 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add New Project
          </button>
        </div>

        {/* Conditionally render the AddEditProjectForm or the projects list/filters */}
        {showAddForm ? (
          <AddEditProjectForm 
            projectId={editProjectId} 
            onClose={() => handleFormClose(true)} 
          />
        ) : (
          <>
            {/* Filters */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search projects by name or RERA number..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  <select 
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Categories</option>
                    <option value="residential">Residential</option>
                    <option value="commercial">Commercial</option>
                  </select>
                  <select 
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="completed">Completed</option>
                    <option value="ongoing">Ongoing</option>
                    <option value="upcoming">Upcoming</option>
                  </select>
                  <button 
                    onClick={resetFilters}
                    className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Filter className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
            
            {/* Projects Table */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading projects...</p>
                </div>
              ) : filteredProjects.length === 0 ? (
                <div className="p-8 text-center">
                  <p className="text-gray-600 mb-4">No projects found matching your criteria.</p>
                  <button 
                    onClick={resetFilters}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Reset Filters
                  </button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Project
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          RERA
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredProjects.map((project) => (
                        <tr key={project.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <img
                                src={project.coverImage}
                                alt={project.title}
                                className="w-10 h-10 rounded-lg object-cover mr-3"
                              />
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {project.title}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {project.location?.address || 'No location specified'}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCategoryColor(project.category)}`}
                            >
                              {project.category.charAt(0).toUpperCase() + project.category.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.status)}`}
                            >
                              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-900">
                              {project.reraNumber || 'N/A'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-900">
                              {formatDate(project.createdAt)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center space-x-2">
                              <Link href={`/projects/${project.id}`} target="_blank">
                                <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                                  <Eye className="w-4 h-4" />
                                </button>
                              </Link>
                              <button 
                                onClick={() => handleEdit(project.id)}
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button 
                                onClick={() => handleDelete(project.id)}
                                className={`p-1 ${confirmDelete === project.id ? 'text-red-600' : 'text-gray-400 hover:text-red-600'} transition-colors`}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                              {project.brochureUrl && (
                                <a 
                                  href={project.brochureUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="p-1 text-gray-400 hover:text-indigo-600 transition-colors"
                                >
                                  <DownloadCloud className="w-4 h-4" />
                                </a>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </>
  )
}
